import pandas as pd
import numpy as np

def process_student_data():
    """
    处理学生课堂成绩数据
    1. 数据清洗：如果student_num和question_num同时相同，则保留score较高的那一行数据
    2. 对student_num进行合并：提取name，student_num，score为该同学所有成绩中最高6次成绩的平均分
    3. 保存为lab_poll_final.xlsx，包含name，student_num和score三列
    """
    
    # 读取原始数据
    print("正在读取原始数据...")
    df = pd.read_excel('lab_poll_person_info.xlsx')
    
    print(f"原始数据形状: {df.shape}")
    print("原始数据列名:", df.columns.tolist())
    print("\n原始数据前5行:")
    print(df.head())
    
    # 检查数据中是否有重复的student_num和question_num组合
    print("\n检查重复数据...")
    duplicates = df.duplicated(subset=['student_num', 'question_num'], keep=False)
    print(f"发现重复记录数量: {duplicates.sum()}")
    
    if duplicates.sum() > 0:
        print("重复记录示例:")
        print(df[duplicates].head(10))
    
    # 步骤1: 数据清洗 - 对于相同的student_num和question_num，保留score较高的记录
    print("\n步骤1: 数据清洗...")
    df_cleaned = df.loc[df.groupby(['student_num', 'question_num'])['score'].idxmax()]
    
    print(f"清洗后数据形状: {df_cleaned.shape}")
    print(f"清洗掉的记录数: {df.shape[0] - df_cleaned.shape[0]}")
    
    # 步骤2: 对student_num进行合并，计算每个学生最高6次成绩的平均分
    print("\n步骤2: 计算每个学生最高6次成绩的平均分...")
    
    def get_top6_average(group):
        """获取每个学生最高6次成绩的平均分"""
        # 按score降序排列，取前6个
        top_scores = group.nlargest(6, 'score')
        return pd.Series({
            'name': group['name'].iloc[0],  # 假设同一个student_num对应同一个name
            'student_num': group['student_num'].iloc[0],
            'score': top_scores['score'].mean()
        })
    
    # 按student_num分组，计算每组的最高6次成绩平均分
    df_final = df_cleaned.groupby('student_num').apply(get_top6_average).reset_index(drop=True)
    
    print(f"最终数据形状: {df_final.shape}")
    print("\n最终数据前10行:")
    print(df_final.head(10))
    
    # 检查数据统计信息
    print("\n数据统计信息:")
    print(f"学生总数: {len(df_final)}")
    print(f"平均分统计:")
    print(df_final['score'].describe())
    
    # 步骤3: 保存为Excel文件
    print("\n步骤3: 保存最终结果...")
    df_final.to_excel('lab_poll_final.xlsx', index=False)
    print("数据已保存到 lab_poll_final.xlsx")
    
    return df_final

if __name__ == "__main__":
    result = process_student_data()
